{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/productivity/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { \n  Activity, \n  TrendingUp, \n  Users, \n  Clock, \n  Target,\n  BarChart3,\n  Calendar,\n  Zap\n} from 'lucide-react'\n\nexport default function ProductivityPage() {\n  const [selectedPeriod, setSelectedPeriod] = useState('week')\n\n  // Dati mock per la demo\n  const productivityData = {\n    totalCables: 1250,\n    installedCables: 890,\n    connectedCables: 650,\n    certifiedCables: 420,\n    activeTeams: 8,\n    avgInstallationRate: 12.5, // cavi/ora\n    avgConnectionRate: 8.3,    // cavi/ora\n    avgCertificationRate: 6.1, // cavi/ora\n    completionPercentage: 71.2,\n    estimatedCompletion: '15 giorni'\n  }\n\n  const teamStats = [\n    { name: 'Team Alpha', members: 4, installed: 156, connected: 98, certified: 67, efficiency: 92 },\n    { name: 'Team Beta', members: 3, installed: 134, connected: 89, certified: 54, efficiency: 88 },\n    { name: 'Team Gamma', members: 5, installed: 189, connected: 145, certified: 89, efficiency: 95 },\n    { name: 'Team Delta', members: 4, installed: 167, connected: 123, certified: 78, efficiency: 90 }\n  ]\n\n  const recentActivities = [\n    { time: '10:30', action: 'Installazione completata', details: 'Cavo FG16OM4-24 - Settore A', team: 'Alpha' },\n    { time: '10:15', action: 'Collegamento certificato', details: 'Cavo MM-OM3-12 - Settore B', team: 'Beta' },\n    { time: '09:45', action: 'Nuova installazione', details: 'Cavo SM-G652D-48 - Settore C', team: 'Gamma' },\n    { time: '09:30', action: 'Test completato', details: 'Cavo FG16OM4-12 - Settore A', team: 'Delta' }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        \n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-slate-900 flex items-center gap-3\">\n              <Activity className=\"h-8 w-8 text-blue-600\" />\n              Dashboard Produttività\n            </h1>\n            <p className=\"text-slate-600 mt-1\">Monitoraggio avanzamento cantiere in tempo reale</p>\n          </div>\n          \n          <div className=\"flex gap-2\">\n            {['day', 'week', 'month'].map((period) => (\n              <Button\n                key={period}\n                variant={selectedPeriod === period ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedPeriod(period)}\n                className=\"capitalize\"\n              >\n                {period === 'day' ? 'Oggi' : period === 'week' ? 'Settimana' : 'Mese'}\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* KPI Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card className=\"border-l-4 border-l-blue-500\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-slate-600\">Avanzamento Totale</CardTitle>\n              <Target className=\"h-4 w-4 text-blue-500\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-slate-900\">{productivityData.completionPercentage}%</div>\n              <Progress value={productivityData.completionPercentage} className=\"mt-2\" />\n              <p className=\"text-xs text-slate-500 mt-2\">\n                {productivityData.installedCables} di {productivityData.totalCables} cavi\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-l-4 border-l-green-500\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-slate-600\">Velocità Installazione</CardTitle>\n              <Zap className=\"h-4 w-4 text-green-500\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-slate-900\">{productivityData.avgInstallationRate}</div>\n              <p className=\"text-xs text-slate-500\">cavi/ora per persona</p>\n              <div className=\"flex items-center mt-2\">\n                <TrendingUp className=\"h-3 w-3 text-green-500 mr-1\" />\n                <span className=\"text-xs text-green-600\">+12% vs settimana scorsa</span>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-l-4 border-l-orange-500\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-slate-600\">Team Attivi</CardTitle>\n              <Users className=\"h-4 w-4 text-orange-500\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-slate-900\">{productivityData.activeTeams}</div>\n              <p className=\"text-xs text-slate-500\">squadre operative</p>\n              <div className=\"flex items-center mt-2\">\n                <Badge variant=\"secondary\" className=\"text-xs\">16 persone</Badge>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-l-4 border-l-purple-500\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-slate-600\">Completamento Stimato</CardTitle>\n              <Calendar className=\"h-4 w-4 text-purple-500\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-slate-900\">{productivityData.estimatedCompletion}</div>\n              <p className=\"text-xs text-slate-500\">al ritmo attuale</p>\n              <div className=\"flex items-center mt-2\">\n                <Clock className=\"h-3 w-3 text-purple-500 mr-1\" />\n                <span className=\"text-xs text-purple-600\">Aggiornato ora</span>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Team Performance */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <BarChart3 className=\"h-5 w-5 text-blue-600\" />\n                Performance Team\n              </CardTitle>\n              <CardDescription>Statistiche dettagliate per squadra</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {teamStats.map((team, index) => (\n                  <div key={index} className=\"flex items-center justify-between p-3 bg-slate-50 rounded-lg\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <h4 className=\"font-medium text-slate-900\">{team.name}</h4>\n                        <Badge variant={team.efficiency >= 90 ? 'default' : 'secondary'}>\n                          {team.efficiency}% efficienza\n                        </Badge>\n                      </div>\n                      <div className=\"grid grid-cols-3 gap-4 text-sm text-slate-600\">\n                        <div>Installati: <span className=\"font-medium text-slate-900\">{team.installed}</span></div>\n                        <div>Collegati: <span className=\"font-medium text-slate-900\">{team.connected}</span></div>\n                        <div>Certificati: <span className=\"font-medium text-slate-900\">{team.certified}</span></div>\n                      </div>\n                      <Progress value={team.efficiency} className=\"mt-2 h-2\" />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Activity className=\"h-5 w-5 text-green-600\" />\n                Attività Recenti\n              </CardTitle>\n              <CardDescription>Ultime operazioni completate</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {recentActivities.map((activity, index) => (\n                  <div key={index} className=\"flex items-start gap-3 p-3 bg-slate-50 rounded-lg\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0\"></div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center justify-between\">\n                        <p className=\"text-sm font-medium text-slate-900\">{activity.action}</p>\n                        <span className=\"text-xs text-slate-500\">{activity.time}</span>\n                      </div>\n                      <p className=\"text-sm text-slate-600 truncate\">{activity.details}</p>\n                      <Badge variant=\"outline\" className=\"mt-1 text-xs\">\n                        {activity.team}\n                      </Badge>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAkBe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,wBAAwB;IACxB,MAAM,mBAAmB;QACvB,aAAa;QACb,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,aAAa;QACb,qBAAqB;QACrB,mBAAmB;QACnB,sBAAsB;QACtB,sBAAsB;QACtB,qBAAqB;IACvB;IAEA,MAAM,YAAY;QAChB;YAAE,MAAM;YAAc,SAAS;YAAG,WAAW;YAAK,WAAW;YAAI,WAAW;YAAI,YAAY;QAAG;QAC/F;YAAE,MAAM;YAAa,SAAS;YAAG,WAAW;YAAK,WAAW;YAAI,WAAW;YAAI,YAAY;QAAG;QAC9F;YAAE,MAAM;YAAc,SAAS;YAAG,WAAW;YAAK,WAAW;YAAK,WAAW;YAAI,YAAY;QAAG;QAChG;YAAE,MAAM;YAAc,SAAS;YAAG,WAAW;YAAK,WAAW;YAAK,WAAW;YAAI,YAAY;QAAG;KACjG;IAED,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAS,QAAQ;YAA4B,SAAS;YAA+B,MAAM;QAAQ;QAC3G;YAAE,MAAM;YAAS,QAAQ;YAA4B,SAAS;YAA8B,MAAM;QAAO;QACzG;YAAE,MAAM;YAAS,QAAQ;YAAuB,SAAS;YAAgC,MAAM;QAAQ;QACvG;YAAE,MAAM;YAAS,QAAQ;YAAmB,SAAS;YAA+B,MAAM;QAAQ;KACnG;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAGhD,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAGrC,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAO;gCAAQ;6BAAQ,CAAC,GAAG,CAAC,CAAC,uBAC7B,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,mBAAmB,SAAS,YAAY;oCACjD,MAAK;oCACL,SAAS,IAAM,kBAAkB;oCACjC,WAAU;8CAET,WAAW,QAAQ,SAAS,WAAW,SAAS,cAAc;mCAN1D;;;;;;;;;;;;;;;;8BAab,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAqC;;;;;;sDAC1D,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;8CAEpB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;;gDAAqC,iBAAiB,oBAAoB;gDAAC;;;;;;;sDAC1F,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,OAAO,iBAAiB,oBAAoB;4CAAE,WAAU;;;;;;sDAClE,6LAAC;4CAAE,WAAU;;gDACV,iBAAiB,eAAe;gDAAC;gDAAK,iBAAiB,WAAW;gDAAC;;;;;;;;;;;;;;;;;;;sCAK1E,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAqC;;;;;;sDAC1D,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;8CAEjB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAqC,iBAAiB,mBAAmB;;;;;;sDACxF,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;sCAK/C,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAqC;;;;;;sDAC1D,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAqC,iBAAiB,WAAW;;;;;;sDAChF,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAqC;;;;;;sDAC1D,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAqC,iBAAiB,mBAAmB;;;;;;sDACxF,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOlD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAA0B;;;;;;;sDAGjD,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;gDAAgB,WAAU;0DACzB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA8B,KAAK,IAAI;;;;;;8EACrD,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAS,KAAK,UAAU,IAAI,KAAK,YAAY;;wEACjD,KAAK,UAAU;wEAAC;;;;;;;;;;;;;sEAGrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAI;sFAAY,6LAAC;4EAAK,WAAU;sFAA8B,KAAK,SAAS;;;;;;;;;;;;8EAC7E,6LAAC;;wEAAI;sFAAW,6LAAC;4EAAK,WAAU;sFAA8B,KAAK,SAAS;;;;;;;;;;;;8EAC5E,6LAAC;;wEAAI;sFAAa,6LAAC;4EAAK,WAAU;sFAA8B,KAAK,SAAS;;;;;;;;;;;;;;;;;;sEAEhF,6LAAC,uIAAA,CAAA,WAAQ;4DAAC,OAAO,KAAK,UAAU;4DAAE,WAAU;;;;;;;;;;;;+CAbtC;;;;;;;;;;;;;;;;;;;;;sCAqBlB,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;sDAGjD,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAsC,SAAS,MAAM;;;;;;kFAClE,6LAAC;wEAAK,WAAU;kFAA0B,SAAS,IAAI;;;;;;;;;;;;0EAEzD,6LAAC;gEAAE,WAAU;0EAAmC,SAAS,OAAO;;;;;;0EAChE,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAChC,SAAS,IAAI;;;;;;;;;;;;;+CATV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsB5B;GA5LwB;KAAA", "debugId": null}}]}