{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { \n  Cable, \n  Search, \n  Filter, \n  Plus, \n  Edit, \n  Trash2, \n  CheckCircle, \n  Clock, \n  AlertCircle,\n  Eye,\n  Download,\n  Upload\n} from 'lucide-react'\n\nexport default function CaviPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n\n  // Dati mock per i cavi\n  const cavi = [\n    {\n      id: 1,\n      nomenclatura: 'FG16OM4-24-001',\n      tipologia: 'Fibra Ottica',\n      formazione: '24 fibre OM4',\n      lunghezza_progetto: 150,\n      metri_installati: 150,\n      bobina: 'BOB-2024-001',\n      stato: 'installato',\n      collegamento: 'collegato',\n      certificazione: 'certificato',\n      settore: 'A',\n      data_installazione: '2024-01-15'\n    },\n    {\n      id: 2,\n      nomenclatura: 'MM-OM3-12-002',\n      tipologia: 'Fibra Ottica',\n      formazione: '12 fibre OM3',\n      lunghezza_progetto: 200,\n      metri_installati: 180,\n      bobina: 'BOB-2024-002',\n      stato: 'in_corso',\n      collegamento: 'non_collegato',\n      certificazione: 'non_certificato',\n      settore: 'B',\n      data_installazione: '2024-01-16'\n    },\n    {\n      id: 3,\n      nomenclatura: 'SM-G652D-48-003',\n      tipologia: 'Fibra Ottica',\n      formazione: '48 fibre G652D',\n      lunghezza_progetto: 300,\n      metri_installati: 0,\n      bobina: 'BOBINA_VUOTA',\n      stato: 'non_installato',\n      collegamento: 'non_collegato',\n      certificazione: 'non_certificato',\n      settore: 'C',\n      data_installazione: null\n    },\n    {\n      id: 4,\n      nomenclatura: 'UTP-CAT6-004',\n      tipologia: 'Rame',\n      formazione: 'CAT6 UTP',\n      lunghezza_progetto: 100,\n      metri_installati: 100,\n      bobina: 'BOB-2024-003',\n      stato: 'installato',\n      collegamento: 'parzialmente_collegato',\n      certificazione: 'in_corso',\n      settore: 'A',\n      data_installazione: '2024-01-17'\n    }\n  ]\n\n  const getStatusBadge = (stato: string) => {\n    switch (stato) {\n      case 'installato':\n        return <Badge className=\"bg-green-100 text-green-800\">Installato</Badge>\n      case 'in_corso':\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">In Corso</Badge>\n      case 'non_installato':\n        return <Badge className=\"bg-gray-100 text-gray-800\">Non Installato</Badge>\n      default:\n        return <Badge variant=\"secondary\">{stato}</Badge>\n    }\n  }\n\n  const getConnectionBadge = (collegamento: string) => {\n    switch (collegamento) {\n      case 'collegato':\n        return <Badge className=\"bg-green-100 text-green-800\">Collegato</Badge>\n      case 'parzialmente_collegato':\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">Parziale</Badge>\n      case 'non_collegato':\n        return <Badge className=\"bg-gray-100 text-gray-800\">Non Collegato</Badge>\n      default:\n        return <Badge variant=\"secondary\">{collegamento}</Badge>\n    }\n  }\n\n  const getCertificationBadge = (certificazione: string) => {\n    switch (certificazione) {\n      case 'certificato':\n        return <Badge className=\"bg-green-100 text-green-800\">Certificato</Badge>\n      case 'in_corso':\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">In Corso</Badge>\n      case 'non_certificato':\n        return <Badge className=\"bg-gray-100 text-gray-800\">Non Certificato</Badge>\n      default:\n        return <Badge variant=\"secondary\">{certificazione}</Badge>\n    }\n  }\n\n  const filteredCavi = cavi.filter(cavo => {\n    const matchesSearch = cavo.nomenclatura.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         cavo.formazione.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesStatus = selectedStatus === 'all' || cavo.stato === selectedStatus\n    \n    return matchesSearch && matchesStatus\n  })\n\n  const stats = {\n    totali: cavi.length,\n    installati: cavi.filter(c => c.stato === 'installato').length,\n    in_corso: cavi.filter(c => c.stato === 'in_corso').length,\n    non_installati: cavi.filter(c => c.stato === 'non_installato').length\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        \n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-slate-900 flex items-center gap-3\">\n              <Cable className=\"h-8 w-8 text-blue-600\" />\n              Gestione Cavi\n            </h1>\n            <p className=\"text-slate-600 mt-1\">Visualizzazione e gestione completa dei cavi del cantiere</p>\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Esporta\n            </Button>\n            <Button variant=\"outline\" size=\"sm\">\n              <Upload className=\"h-4 w-4 mr-2\" />\n              Importa\n            </Button>\n            <Button size=\"sm\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Nuovo Cavo\n            </Button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Totali</p>\n                  <p className=\"text-2xl font-bold text-slate-900\">{stats.totali}</p>\n                </div>\n                <Cable className=\"h-8 w-8 text-blue-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Installati</p>\n                  <p className=\"text-2xl font-bold text-green-600\">{stats.installati}</p>\n                </div>\n                <CheckCircle className=\"h-8 w-8 text-green-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">In Corso</p>\n                  <p className=\"text-2xl font-bold text-yellow-600\">{stats.in_corso}</p>\n                </div>\n                <Clock className=\"h-8 w-8 text-yellow-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Da Installare</p>\n                  <p className=\"text-2xl font-bold text-gray-600\">{stats.non_installati}</p>\n                </div>\n                <AlertCircle className=\"h-8 w-8 text-gray-500\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters and Search */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Search className=\"h-5 w-5\" />\n              Ricerca e Filtri\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex gap-4\">\n              <div className=\"flex-1\">\n                <Input\n                  placeholder=\"Cerca per nomenclatura, tipologia o formazione...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full\"\n                />\n              </div>\n              <div className=\"flex gap-2\">\n                {['all', 'installato', 'in_corso', 'non_installato'].map((status) => (\n                  <Button\n                    key={status}\n                    variant={selectedStatus === status ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedStatus(status)}\n                  >\n                    {status === 'all' ? 'Tutti' : \n                     status === 'installato' ? 'Installati' :\n                     status === 'in_corso' ? 'In Corso' : 'Da Installare'}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Cavi Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Elenco Cavi ({filteredCavi.length})</CardTitle>\n            <CardDescription>\n              Gestione completa dei cavi con stato installazione, collegamento e certificazione\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Nomenclatura</TableHead>\n                    <TableHead>Tipologia</TableHead>\n                    <TableHead>Formazione</TableHead>\n                    <TableHead>Lunghezza</TableHead>\n                    <TableHead>Bobina</TableHead>\n                    <TableHead>Stato</TableHead>\n                    <TableHead>Collegamento</TableHead>\n                    <TableHead>Certificazione</TableHead>\n                    <TableHead>Settore</TableHead>\n                    <TableHead>Azioni</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {filteredCavi.map((cavo) => (\n                    <TableRow key={cavo.id}>\n                      <TableCell className=\"font-medium\">{cavo.nomenclatura}</TableCell>\n                      <TableCell>{cavo.tipologia}</TableCell>\n                      <TableCell>{cavo.formazione}</TableCell>\n                      <TableCell>\n                        <div className=\"text-sm\">\n                          <div>{cavo.metri_installati}/{cavo.lunghezza_progetto}m</div>\n                          <div className=\"text-slate-500\">\n                            {Math.round((cavo.metri_installati / cavo.lunghezza_progetto) * 100)}%\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <Badge variant={cavo.bobina === 'BOBINA_VUOTA' ? 'destructive' : 'secondary'}>\n                          {cavo.bobina}\n                        </Badge>\n                      </TableCell>\n                      <TableCell>{getStatusBadge(cavo.stato)}</TableCell>\n                      <TableCell>{getConnectionBadge(cavo.collegamento)}</TableCell>\n                      <TableCell>{getCertificationBadge(cavo.certificazione)}</TableCell>\n                      <TableCell>\n                        <Badge variant=\"outline\">{cavo.settore}</Badge>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex gap-1\">\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <Eye className=\"h-4 w-4\" />\n                          </Button>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <Trash2 className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          </CardContent>\n        </Card>\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;AAwBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,uBAAuB;IACvB,MAAM,OAAO;QACX;YACE,IAAI;YACJ,cAAc;YACd,WAAW;YACX,YAAY;YACZ,oBAAoB;YACpB,kBAAkB;YAClB,QAAQ;YACR,OAAO;YACP,cAAc;YACd,gBAAgB;YAChB,SAAS;YACT,oBAAoB;QACtB;QACA;YACE,IAAI;YACJ,cAAc;YACd,WAAW;YACX,YAAY;YACZ,oBAAoB;YACpB,kBAAkB;YAClB,QAAQ;YACR,OAAO;YACP,cAAc;YACd,gBAAgB;YAChB,SAAS;YACT,oBAAoB;QACtB;QACA;YACE,IAAI;YACJ,cAAc;YACd,WAAW;YACX,YAAY;YACZ,oBAAoB;YACpB,kBAAkB;YAClB,QAAQ;YACR,OAAO;YACP,cAAc;YACd,gBAAgB;YAChB,SAAS;YACT,oBAAoB;QACtB;QACA;YACE,IAAI;YACJ,cAAc;YACd,WAAW;YACX,YAAY;YACZ,oBAAoB;YACpB,kBAAkB;YAClB,QAAQ;YACR,OAAO;YACP,cAAc;YACd,gBAAgB;YAChB,SAAS;YACT,oBAAoB;QACtB;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA;QAC/B,MAAM,gBAAgB,KAAK,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAChE,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,KAAK,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAElF,MAAM,gBAAgB,mBAAmB,SAAS,KAAK,KAAK,KAAK;QAEjE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,QAAQ;QACZ,QAAQ,KAAK,MAAM;QACnB,YAAY,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,cAAc,MAAM;QAC7D,UAAU,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,YAAY,MAAM;QACzD,gBAAgB,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,kBAAkB,MAAM;IACvE;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAG7C,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAGrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;;sDACX,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,8OAAC;oDAAE,WAAU;8DAAqC,MAAM,MAAM;;;;;;;;;;;;sDAEhE,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,8OAAC;oDAAE,WAAU;8DAAqC,MAAM,UAAU;;;;;;;;;;;;sDAEpE,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK7B,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,8OAAC;oDAAE,WAAU;8DAAsC,MAAM,QAAQ;;;;;;;;;;;;sDAEnE,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,8OAAC;oDAAE,WAAU;8DAAoC,MAAM,cAAc;;;;;;;;;;;;sDAEvE,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO/B,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIlC,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAO;4CAAc;4CAAY;yCAAiB,CAAC,GAAG,CAAC,CAAC,uBACxD,8OAAC,kIAAA,CAAA,SAAM;gDAEL,SAAS,mBAAmB,SAAS,YAAY;gDACjD,MAAK;gDACL,SAAS,IAAM,kBAAkB;0DAEhC,WAAW,QAAQ,UACnB,WAAW,eAAe,eAC1B,WAAW,aAAa,aAAa;+CAPjC;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgBjB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;;wCAAC;wCAAc,aAAa,MAAM;wCAAC;;;;;;;8CAC7C,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sDACJ,8OAAC,iIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,iIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;;;;;;sDAGf,8OAAC,iIAAA,CAAA,YAAS;sDACP,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,iIAAA,CAAA,WAAQ;;sEACP,8OAAC,iIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAe,KAAK,YAAY;;;;;;sEACrD,8OAAC,iIAAA,CAAA,YAAS;sEAAE,KAAK,SAAS;;;;;;sEAC1B,8OAAC,iIAAA,CAAA,YAAS;sEAAE,KAAK,UAAU;;;;;;sEAC3B,8OAAC,iIAAA,CAAA,YAAS;sEACR,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;4EAAK,KAAK,gBAAgB;4EAAC;4EAAE,KAAK,kBAAkB;4EAAC;;;;;;;kFACtD,8OAAC;wEAAI,WAAU;;4EACZ,KAAK,KAAK,CAAC,AAAC,KAAK,gBAAgB,GAAG,KAAK,kBAAkB,GAAI;4EAAK;;;;;;;;;;;;;;;;;;sEAI3E,8OAAC,iIAAA,CAAA,YAAS;sEACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAS,KAAK,MAAM,KAAK,iBAAiB,gBAAgB;0EAC9D,KAAK,MAAM;;;;;;;;;;;sEAGhB,8OAAC,iIAAA,CAAA,YAAS;sEAAE,eAAe,KAAK,KAAK;;;;;;sEACrC,8OAAC,iIAAA,CAAA,YAAS;sEAAE,mBAAmB,KAAK,YAAY;;;;;;sEAChD,8OAAC,iIAAA,CAAA,YAAS;sEAAE,sBAAsB,KAAK,cAAc;;;;;;sEACrD,8OAAC,iIAAA,CAAA,YAAS;sEACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW,KAAK,OAAO;;;;;;;;;;;sEAExC,8OAAC,iIAAA,CAAA,YAAS;sEACR,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,8OAAC,2MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDAhCX,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+C1C", "debugId": null}}]}