{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { caviApi } from '@/lib/api'\nimport { Cavo } from '@/types'\nimport {\n  Cable,\n  Search,\n  Filter,\n  Plus,\n  Edit,\n  Trash2,\n  CheckCircle,\n  Clock,\n  AlertCircle,\n  Eye,\n  Download,\n  Upload,\n  Loader2\n} from 'lucide-react'\n\nexport default function CaviPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  const { user, cantiere } = useAuth()\n\n  // Carica i cavi dal backend\n  useEffect(() => {\n    loadCavi()\n  }, [])\n\n  const loadCavi = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n\n      const cantiereId = cantiere?.id_cantiere || user?.id_utente\n      if (!cantiereId) {\n        setError('Cantiere non selezionato')\n        return\n      }\n\n      const data = await caviApi.getCavi(cantiereId, {\n        search: searchTerm,\n        stato_installazione: selectedStatus === 'all' ? undefined : selectedStatus\n      })\n\n      setCavi(data)\n    } catch (error: any) {\n      console.error('Errore caricamento cavi:', error)\n      setError(error.response?.data?.detail || 'Errore durante il caricamento dei cavi')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Ricarica quando cambiano i filtri\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      loadCavi()\n    }, 500) // Debounce di 500ms\n\n    return () => clearTimeout(timeoutId)\n  }, [searchTerm, selectedStatus])\n\n  const getStatusBadge = (stato: string | undefined) => {\n    switch (stato) {\n      case 'installato':\n        return <Badge className=\"bg-green-100 text-green-800\">Installato</Badge>\n      case 'in_corso':\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">In Corso</Badge>\n      case 'non_installato':\n      case '':\n      case null:\n      case undefined:\n        return <Badge className=\"bg-gray-100 text-gray-800\">Non Installato</Badge>\n      default:\n        return <Badge variant=\"secondary\">{stato}</Badge>\n    }\n  }\n\n  const getConnectionBadge = (collegamenti: number | undefined) => {\n    switch (collegamenti) {\n      case 3:\n        return <Badge className=\"bg-green-100 text-green-800\">Collegato</Badge>\n      case 1:\n      case 2:\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">Parziale</Badge>\n      case 0:\n      case null:\n      case undefined:\n        return <Badge className=\"bg-gray-100 text-gray-800\">Non Collegato</Badge>\n      default:\n        return <Badge variant=\"secondary\">Stato {collegamenti}</Badge>\n    }\n  }\n\n  const getCertificationBadge = (cavo: Cavo) => {\n    if (cavo.data_certificazione) {\n      return <Badge className=\"bg-green-100 text-green-800\">Certificato</Badge>\n    } else if (cavo.comanda_certificazione) {\n      return <Badge className=\"bg-yellow-100 text-yellow-800\">In Corso</Badge>\n    } else {\n      return <Badge className=\"bg-gray-100 text-gray-800\">Non Certificato</Badge>\n    }\n  }\n\n  const filteredCavi = cavi.filter(cavo => {\n    const matchesSearch = cavo.id_cavo?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         cavo.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         cavo.n_conduttori?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         cavo.sezione?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    const matchesStatus = selectedStatus === 'all' || cavo.stato_installazione === selectedStatus\n\n    return matchesSearch && matchesStatus\n  })\n\n  const stats = {\n    totali: cavi.length,\n    installati: cavi.filter(c => c.metratura_reale && c.metratura_reale > 0).length,\n    in_corso: cavi.filter(c => c.comanda_posa && !c.data_posa).length,\n    non_installati: cavi.filter(c => !c.metratura_reale || c.metratura_reale === 0).length\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        \n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-slate-900 flex items-center gap-3\">\n              <Cable className=\"h-8 w-8 text-blue-600\" />\n              Gestione Cavi\n            </h1>\n            <p className=\"text-slate-600 mt-1\">Visualizzazione e gestione completa dei cavi del cantiere</p>\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Esporta\n            </Button>\n            <Button variant=\"outline\" size=\"sm\">\n              <Upload className=\"h-4 w-4 mr-2\" />\n              Importa\n            </Button>\n            <Button size=\"sm\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Nuovo Cavo\n            </Button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Totali</p>\n                  <p className=\"text-2xl font-bold text-slate-900\">{stats.totali}</p>\n                </div>\n                <Cable className=\"h-8 w-8 text-blue-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Installati</p>\n                  <p className=\"text-2xl font-bold text-green-600\">{stats.installati}</p>\n                </div>\n                <CheckCircle className=\"h-8 w-8 text-green-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">In Corso</p>\n                  <p className=\"text-2xl font-bold text-yellow-600\">{stats.in_corso}</p>\n                </div>\n                <Clock className=\"h-8 w-8 text-yellow-500\" />\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-slate-600\">Da Installare</p>\n                  <p className=\"text-2xl font-bold text-gray-600\">{stats.non_installati}</p>\n                </div>\n                <AlertCircle className=\"h-8 w-8 text-gray-500\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters and Search */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Search className=\"h-5 w-5\" />\n              Ricerca e Filtri\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex gap-4\">\n              <div className=\"flex-1\">\n                <Input\n                  placeholder=\"Cerca per nomenclatura, tipologia o formazione...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full\"\n                />\n              </div>\n              <div className=\"flex gap-2\">\n                {['all', 'installato', 'in_corso', 'non_installato'].map((status) => (\n                  <Button\n                    key={status}\n                    variant={selectedStatus === status ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedStatus(status)}\n                  >\n                    {status === 'all' ? 'Tutti' : \n                     status === 'installato' ? 'Installati' :\n                     status === 'in_corso' ? 'In Corso' : 'Da Installare'}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Cavi Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Elenco Cavi ({filteredCavi.length})</CardTitle>\n            <CardDescription>\n              Gestione completa dei cavi con stato installazione, collegamento e certificazione\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>ID Cavo</TableHead>\n                    <TableHead>Tipologia</TableHead>\n                    <TableHead>Conduttori/Sezione</TableHead>\n                    <TableHead>Partenza → Arrivo</TableHead>\n                    <TableHead>Lunghezza</TableHead>\n                    <TableHead>Bobina</TableHead>\n                    <TableHead>Stato</TableHead>\n                    <TableHead>Collegamento</TableHead>\n                    <TableHead>Certificazione</TableHead>\n                    <TableHead>Azioni</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {isLoading ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2\">\n                          <Loader2 className=\"h-4 w-4 animate-spin\" />\n                          Caricamento cavi...\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : error ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2 text-red-600\">\n                          <AlertCircle className=\"h-4 w-4\" />\n                          {error}\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : filteredCavi.length === 0 ? (\n                    <TableRow>\n                      <TableCell colSpan={10} className=\"text-center py-8 text-slate-500\">\n                        Nessun cavo trovato\n                      </TableCell>\n                    </TableRow>\n                  ) : (\n                    filteredCavi.map((cavo) => (\n                      <TableRow key={cavo.id_cavo}>\n                        <TableCell className=\"font-medium\">{cavo.id_cavo}</TableCell>\n                        <TableCell>{cavo.tipologia || '-'}</TableCell>\n                        <TableCell>\n                          <div className=\"text-sm\">\n                            <div>{cavo.n_conduttori || '-'}</div>\n                            <div className=\"text-slate-500\">{cavo.sezione || '-'}</div>\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"text-sm\">\n                            <div className=\"font-medium\">{cavo.ubicazione_partenza || '-'}</div>\n                            <div className=\"text-slate-500\">↓</div>\n                            <div className=\"font-medium\">{cavo.ubicazione_arrivo || '-'}</div>\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"text-sm\">\n                            <div>{cavo.metratura_reale || 0}/{cavo.metri_teorici || 0}m</div>\n                            <div className=\"text-slate-500\">\n                              {cavo.metri_teorici ? Math.round(((cavo.metratura_reale || 0) / cavo.metri_teorici) * 100) : 0}%\n                            </div>\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <Badge variant={cavo.id_bobina === 'BOBINA_VUOTA' ? 'destructive' : 'secondary'}>\n                            {cavo.id_bobina || 'BOBINA_VUOTA'}\n                          </Badge>\n                        </TableCell>\n                        <TableCell>{getStatusBadge(cavo.stato_installazione)}</TableCell>\n                        <TableCell>{getConnectionBadge(cavo.collegamenti)}</TableCell>\n                        <TableCell>{getCertificationBadge(cavo)}</TableCell>\n                        <TableCell>\n                          <div className=\"flex gap-1\">\n                            <Button variant=\"ghost\" size=\"sm\">\n                              <Eye className=\"h-4 w-4\" />\n                            </Button>\n                            <Button variant=\"ghost\" size=\"sm\">\n                              <Edit className=\"h-4 w-4\" />\n                            </Button>\n                            <Button variant=\"ghost\" size=\"sm\">\n                              <Trash2 className=\"h-4 w-4\" />\n                            </Button>\n                          </div>\n                        </TableCell>\n                      </TableRow>\n                    ))\n                  )}\n                </TableBody>\n              </Table>\n            </div>\n          </CardContent>\n        </Card>\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAZA;;;;;;;;;;AA4Be,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEjC,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,aAAa,UAAU,eAAe,MAAM;YAClD,IAAI,CAAC,YAAY;gBACf,SAAS;gBACT;YACF;YAEA,MAAM,OAAO,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,YAAY;gBAC7C,QAAQ;gBACR,qBAAqB,mBAAmB,QAAQ,YAAY;YAC9D;YAEA,QAAQ;QACV,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,YAAY;gDAAW;oBAC3B;gBACF;+CAAG,KAAK,oBAAoB;;YAE5B;sCAAO,IAAM,aAAa;;QAC5B;6BAAG;QAAC;QAAY;KAAe;IAE/B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA4B;;;;;;YACtD;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;;wBAAY;wBAAO;;;;;;;QAC7C;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,KAAK,mBAAmB,EAAE;YAC5B,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA8B;;;;;;QACxD,OAAO,IAAI,KAAK,sBAAsB,EAAE;YACtC,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAgC;;;;;;QAC1D,OAAO;YACL,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA4B;;;;;;QACtD;IACF;IAEA,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA;QAC/B,MAAM,gBAAgB,KAAK,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW,OAC5D,KAAK,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAC7D,KAAK,YAAY,EAAE,cAAc,SAAS,WAAW,WAAW,OAChE,KAAK,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW;QAEhF,MAAM,gBAAgB,mBAAmB,SAAS,KAAK,mBAAmB,KAAK;QAE/E,OAAO,iBAAiB;IAC1B;IAEA,MAAM,QAAQ;QACZ,QAAQ,KAAK,MAAM;QACnB,YAAY,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,IAAI,EAAE,eAAe,GAAG,GAAG,MAAM;QAC/E,UAAU,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM;QACjE,gBAAgB,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,eAAe,IAAI,EAAE,eAAe,KAAK,GAAG,MAAM;IACxF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAG7C,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAGrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;;sDACX,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAOvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAqC,MAAM,MAAM;;;;;;;;;;;;sDAEhE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAqC,MAAM,UAAU;;;;;;;;;;;;sDAEpE,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK7B,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAsC,MAAM,QAAQ;;;;;;;;;;;;sDAEnE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKvB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAoC,MAAM,cAAc;;;;;;;;;;;;sDAEvE,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO/B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIlC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAO;4CAAc;4CAAY;yCAAiB,CAAC,GAAG,CAAC,CAAC,uBACxD,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAS,mBAAmB,SAAS,YAAY;gDACjD,MAAK;gDACL,SAAS,IAAM,kBAAkB;0DAEhC,WAAW,QAAQ,UACnB,WAAW,eAAe,eAC1B,WAAW,aAAa,aAAa;+CAPjC;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgBjB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;;wCAAC;wCAAc,aAAa,MAAM;wCAAC;;;;;;;8CAC7C,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sDACJ,6LAAC,oIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;;;;;;sDAGf,6LAAC,oIAAA,CAAA,YAAS;sDACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oDAAC,SAAS;oDAAI,WAAU;8DAChC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAAyB;;;;;;;;;;;;;;;;uDAKhD,sBACF,6LAAC,oIAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oDAAC,SAAS;oDAAI,WAAU;8DAChC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB;;;;;;;;;;;;;;;;uDAIL,aAAa,MAAM,KAAK,kBAC1B,6LAAC,oIAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oDAAC,SAAS;oDAAI,WAAU;8DAAkC;;;;;;;;;;uDAKtE,aAAa,GAAG,CAAC,CAAC,qBAChB,6LAAC,oIAAA,CAAA,WAAQ;;sEACP,6LAAC,oIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAe,KAAK,OAAO;;;;;;sEAChD,6LAAC,oIAAA,CAAA,YAAS;sEAAE,KAAK,SAAS,IAAI;;;;;;sEAC9B,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK,KAAK,YAAY,IAAI;;;;;;kFAC3B,6LAAC;wEAAI,WAAU;kFAAkB,KAAK,OAAO,IAAI;;;;;;;;;;;;;;;;;sEAGrD,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAe,KAAK,mBAAmB,IAAI;;;;;;kFAC1D,6LAAC;wEAAI,WAAU;kFAAiB;;;;;;kFAChC,6LAAC;wEAAI,WAAU;kFAAe,KAAK,iBAAiB,IAAI;;;;;;;;;;;;;;;;;sEAG5D,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAK,KAAK,eAAe,IAAI;4EAAE;4EAAE,KAAK,aAAa,IAAI;4EAAE;;;;;;;kFAC1D,6LAAC;wEAAI,WAAU;;4EACZ,KAAK,aAAa,GAAG,KAAK,KAAK,CAAC,AAAC,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,KAAK,aAAa,GAAI,OAAO;4EAAE;;;;;;;;;;;;;;;;;;sEAIrG,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,KAAK,SAAS,KAAK,iBAAiB,gBAAgB;0EACjE,KAAK,SAAS,IAAI;;;;;;;;;;;sEAGvB,6LAAC,oIAAA,CAAA,YAAS;sEAAE,eAAe,KAAK,mBAAmB;;;;;;sEACnD,6LAAC,oIAAA,CAAA,YAAS;sEAAE,mBAAmB,KAAK,YAAY;;;;;;sEAChD,6LAAC,oIAAA,CAAA,YAAS;sEAAE,sBAAsB;;;;;;sEAClC,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,6LAAC,8MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDAzCX,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDjD;GA9UwB;;QAOK,kIAAA,CAAA,UAAO;;;KAPZ", "debugId": null}}]}